[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\INetMediator.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/INetMediator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\TcpClientMediator.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/TcpClientMediator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\TcpServerMediator.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/TcpServerMediator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\UdpMediator.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/UdpMediator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\INet.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/INet.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\TcpClient.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/TcpClient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\TcpServer.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/TcpServer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\UdpNet.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/UdpNet.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\MD5\\md5.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/MD5/md5.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\ckernel.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/ckernel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\logindialog.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/logindialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\main.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\roomdialog.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/roomdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\usershow.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/usershow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\2025\\Project\\videoconferencing\\WeChat\\wechatdialog.cpp"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/wechatdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\INetMediator.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/INetMediator.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\TcpClientMediator.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/TcpClientMediator.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\TcpServerMediator.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/TcpServerMediator.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator\\UdpMediator.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/mediator/UdpMediator.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\INet.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/INet.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\TcpClient.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/TcpClient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\TcpServer.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/TcpServer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\UdpNet.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/UdpNet.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net\\packdef.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/netapi/net/packdef.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\MD5\\md5.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/MD5/md5.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\ckernel.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/ckernel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\logindialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/logindialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\roomdialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/roomdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\usershow.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/usershow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\wechatdialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/wechatdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\ui_roomdialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/ui_roomdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\ui_wechatdialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/ui_wechatdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\ui_usershow.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/ui_usershow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\PERSONAL\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\net", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi\\mediator", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\netapi", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\MD5", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtWidgets", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtGui", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtNetwork", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-ID:\\PERSONAL\\QT\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\PERSONAL\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\PERSONAL\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\2025\\Project\\videoconferencing\\WeChat\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\ui_logindialog.h"], "directory": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/2025/Project/videoconferencing/WeChat/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/ui_logindialog.h"}]